.groupCustView {
  display: flex;
  padding: 0 8px;
  overflow: hidden;
  min-height: 100vh;

  .groupCustViewLeft {
    position: relative;
    width: 280px; // 与菜单宽度保持一致
    flex-shrink: 0; // 防止左侧容器被压缩
    background: transparent; // 改为透明，因为菜单已经有背景
    border-radius: 4px;
    overflow: visible; // 允许fixed菜单显示

    :global {
      .ant-card {
        background: transparent;
        box-shadow: none;

        .ant-card-body {
          padding: 0 !important;
        }
      }
    }
  }

  .groupCustViewRight {
    flex: 1;
    margin-left: 8px; // 与左侧保持间距
    background: #FFFFFF;
    border-radius: 4px;
    overflow: auto;
    min-height: calc(100vh - 16px); // 减去上下padding
  }
}
