import React from 'react';
import Frame from './assets/Frame.svg';
import Frame1 from './assets/Frame(1).svg';
import Frame2 from './assets/Frame(2).svg';
import Frame3 from './assets/Frame(3).svg';
import Frame4 from './assets/Frame(4).svg';
import Frame5 from './assets/Frame(5).svg';
import Frame6 from './assets/Frame(6).svg';
import Frame7 from './assets/Frame(7).svg';
import Frame8 from './assets/Frame(8).svg';
import Frame9 from './assets/Frame(9).svg';
import Frame10 from './assets/Frame(10).svg';
import Frame11 from './assets/Frame(11).svg';
import Frame12 from './assets/Frame(12).svg';
import Frame13 from './assets/Frame(13).svg';
import Frame14 from './assets/Frame(14).svg';
import Frame15 from './assets/Frame(15).svg';
import Frame16 from './assets/Frame(16).svg';
import Frame17 from './assets/Frame(17).svg';

// 统一导出SVG图标组件
export const Frame0Icon = ({ style, ...props }) => <img src={Frame} alt="frame" style={{ width: 16, height: 16, ...style }} {...props} />;
export const Frame1Icon = ({ style, ...props }) => <img src={Frame1} alt="frame1" style={{ width: 16, height: 16, ...style }} {...props} />;
export const Frame2Icon = ({ style, ...props }) => <img src={Frame2} alt="frame2" style={{ width: 16, height: 16, ...style }} {...props} />;
export const Frame3Icon = ({ style, ...props }) => <img src={Frame3} alt="frame3" style={{ width: 16, height: 16, ...style }} {...props} />;
export const Frame4Icon = ({ style, ...props }) => <img src={Frame4} alt="frame4" style={{ width: 16, height: 16, ...style }} {...props} />;
export const Frame5Icon = ({ style, ...props }) => <img src={Frame5} alt="frame5" style={{ width: 16, height: 16, ...style }} {...props} />;
export const Frame6Icon = ({ style, ...props }) => <img src={Frame6} alt="frame6" style={{ width: 16, height: 16, ...style }} {...props} />;
export const Frame7Icon = ({ style, ...props }) => <img src={Frame7} alt="frame7" style={{ width: 16, height: 16, ...style }} {...props} />;
export const Frame8Icon = ({ style, ...props }) => <img src={Frame8} alt="frame8" style={{ width: 16, height: 16, ...style }} {...props} />;
export const Frame9Icon = ({ style, ...props }) => <img src={Frame9} alt="frame9" style={{ width: 16, height: 16, ...style }} {...props} />;
export const Frame10Icon = ({ style, ...props }) => <img src={Frame10} alt="frame10" style={{ width: 16, height: 16, ...style }} {...props} />;
export const Frame11Icon = ({ style, ...props }) => <img src={Frame11} alt="frame11" style={{ width: 16, height: 16, ...style }} {...props} />;
export const Frame12Icon = ({ style, ...props }) => <img src={Frame12} alt="frame12" style={{ width: 16, height: 16, ...style }} {...props} />;
export const Frame13Icon = ({ style, ...props }) => <img src={Frame13} alt="frame13" style={{ width: 16, height: 16, ...style }} {...props} />;
export const Frame14Icon = ({ style, ...props }) => <img src={Frame14} alt="frame14" style={{ width: 16, height: 16, ...style }} {...props} />;
export const Frame15Icon = ({ style, ...props }) => <img src={Frame15} alt="frame15" style={{ width: 16, height: 16, ...style }} {...props} />;
export const Frame16Icon = ({ style, ...props }) => <img src={Frame16} alt="frame16" style={{ width: 16, height: 16, ...style }} {...props} />;
export const Frame17Icon = ({ style, ...props }) => <img src={Frame17} alt="frame17" style={{ width: 16, height: 16, ...style }} {...props} />;

// 图标数组，按index索引
const frameIcons = [
  Frame, // index 0
  Frame1, // index 1
  Frame2, // index 2
  Frame3, // index 3
  Frame4, // index 4
  Frame5, // index 5
  Frame6, // index 6
  Frame7, // index 7
  Frame8, // index 8
  Frame9, // index 9
  Frame10, // index 10
  Frame11, // index 11
  Frame12, // index 12
  Frame13, // index 13
  Frame14, // index 14
  Frame15, // index 15
  Frame16, // index 16
  Frame17, // index 17
];

// 统一的图标组件，通过index选择图标
export const FrameIcon = ({ index = 0, style, ...props }) => {
  const iconSrc = frameIcons[index];

  if (!iconSrc) {
    console.warn(`FrameIcon: index ${index} not found, using default (index 0)`);
    return <img src={frameIcons[0]} alt={`frame-${index}`} style={{ width: 16, height: 16, ...style }} {...props} />;
  }

  return <img src={iconSrc} alt={`frame-${index}`} style={{ width: 16, height: 16, ...style }} {...props} />;
};

// 为了向后兼容，保留原有的导出名称
export const OpenSvgIcon = FrameIcon;
