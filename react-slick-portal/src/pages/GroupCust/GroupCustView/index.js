/* 集团客户视图 */
import { connect } from 'dva';
import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import style from './index.less';
import Right from '@/pages/GroupCust/GroupCustView/components/Right';
import LeftMenu from '@/pages/GroupCust/GroupCustView/components/LeftMenu';

const Index = props => {
  const [searchModalVisible, setSearchModalVisible] = useState(false);

  const location = useLocation();
  const { groupId } = location?.query;

  // 路由获取集团客户编码
  useEffect(() => {
    props.dispatch({
      type: 'groupCustView/getCustomerInfo',
      payload: atob(groupId),
    });
  }, [groupId]);


  // 添加键盘快捷键监听
  useEffect(() => {
    const handleKeyDown = e => {
      // 检测 Ctrl+K 组合键
      if ((e.ctrlKey || e.metaKey) && (e.key === 'k' || e.key === 'K')) {
        e.preventDefault();
        setSearchModalVisible(true);
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  return (
    <div className={style.groupCustView}>
      {/* 左侧 */}
      <div className={style.groupCustViewLeft}>
        <LeftMenu
          searchModalVisible={searchModalVisible}
          setSearchModalVisible={setSearchModalVisible}
        />
      </div>

      {/* 右侧 */}
      <div className={style.groupCustViewRight}>
        <Right />
      </div>
    </div>
  );
};
export default connect(() => ({
}))(Index);
